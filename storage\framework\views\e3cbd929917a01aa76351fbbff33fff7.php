

<?php $__env->startSection('content'); ?>
    <div class="card card-custom mb-5">
        <div class="card-body">
            <input type="hidden" id="webhook_id" value="<?php echo e($import_file->id); ?>" />
            <div class="row justify-content-between">
                <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-6">
                    <div class="input-icon">
                        <input type="text" class="form-control" placeholder="Search webhook..." id="webhook_search" />
                        <span>
                            <i class="flaticon2-search-1 text-muted"></i>
                        </span>
                    </div>
                </div>
            </div>


            <div class="datatable datatable-bordered datatable-head-custom" id="webhook_dt"></div>

        </div>
    </div>

    <!-- Log Details Modal -->
    <div class="modal fade" id="webhookDetailsModal" tabindex="-1" role="dialog" aria-labelledby="webhookDetailsModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="webhookDetailsModalLabel">Webhook Details</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <i class="ki ki-close"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="webhook-details-content">
                        <!-- Webhook details will be loaded here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
    <style>
        .text-primary {
            text-decoration: none;
        }

        .text-primary:hover {
            text-decoration: underline;
        }

        #webhookDetailsModal .modal-dialog {
            max-width: 90%;
        }

        #webhook_dt tbody tr td:not(:last-child) {
            cursor: pointer;
        }

        #webhook_dt tbody tr:hover td:not(:last-child) {
            background-color: rgba(54, 153, 255, 0.1) !important;
        }

        .log-context {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .badge-type {
            font-size: 11px;
            padding: 4px 8px;
        }

        .changes-table {
            margin-top: 15px;
        }

        .changes-table table {
            font-size: 13px;
        }

        .changes-table .old-value {
            background-color: #f8d7da;
            color: #721c24;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }

        .changes-table .new-value {
            background-color: #d4edda;
            color: #155724;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }

        .changes-table .field-name {
            font-weight: bold;
            text-transform: capitalize;
        }

        .no-changes {
            font-style: italic;
            color: #6c757d;
        }
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('scripts'); ?>
    <script>
        var datatable;
        var datatableElement;
        var searchElement;
        var columnArray;

        
        const apiRoute = `<?php echo e(route('scripts.webhook.api',['id' => '::ID'])); ?>`;
        const viewRoute = `<?php echo e(route('scripts.webhook.view',['webhook' => '::ID'])); ?>`;

        datatableElement = $('#webhook_dt');
        searchElement = $('#webhook_search');

        const webhookId = $('#webhook_id').val();
     

        columnArray = [{
                field: 'timestamp',
                title: 'Timestamp',
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    let timestamp = data.timestamp;
                    return moment.utc(timestamp).local().format('MM/DD/YYYY hh:mm A');
                }
            },
            {
                field: 'ip_address',
                title: 'IP Address',
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    return data.ip_address || '-';
                }
            },
            {
                field: 'order_id',
                title: 'Order ID',
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    return data.order_id || '-';
                }
            },
            {
                field: 'Actions',
                title: 'Actions',
                width: 'auto',
                sortable: false,
                overflow: 'visible',
                autoHide: false,
                template: function(data) {
                    return `<button class="btn btn-sm btn-clean btn-icon view-details-btn"
                               data-id="${data.id}" title="View Details">
                            <i class="menu-icon fas fa-eye"></i>
                        </button>`;
                }
            }
        ];

        datatable = datatableElement.KTDatatable({
            data: {
                type: 'remote',
                source: {
                    read: {
                        url: apiRoute.replace('::ID', webhookId),
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        },
                        params: function() {
                            const query = datatable.getDataSourceQuery();
                            const searchValue = $(searchElement).val() || '';
                            

                            const params = {
                                search: searchValue,
                                query: {
                                    search: searchValue
                                }
                            };

                            // Add any additional query parameters from datatable
                            if (query && query.query) {
                                Object.assign(params.query, query.query);
                            }

                            return params;
                        },
                        map: function(raw) {
                            var dataSet = raw;
                            if (typeof raw.data !== 'undefined') {
                                dataSet = raw.data;
                            }
                            return dataSet;
                        }
                    },
                },
                pageSize: 25,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            pagination: true,
            search: {
                input: searchElement,
                key: 'search',
                delay: 500
            },
            layout: {
                customScrollbar: false,
                scroll: true,
            },
            columns: columnArray
        });

        // Initialize the datatable query parameters
        datatable.setDataSourceQuery({
            query: {
                type: '',
                user_type: '',
                date_from: '',
                date_to: '',
                search: ''
            }
        });

        // Fix pagination dropdown issue - reinitialize selectpicker after datatable updates
        datatable.on('datatable-on-layout-updated', function() {
            setTimeout(function() {
                // Reinitialize selectpicker for pagination dropdown
                $('.datatable-pager-size.selectpicker').selectpicker('refresh');
            }, 100);
        });

        // Also reinitialize after data reload
        datatable.on('datatable-on-reloaded', function() {
            setTimeout(function() {
                // Reinitialize selectpicker for pagination dropdown
                $('.datatable-pager-size.selectpicker').selectpicker('refresh');
            }, 100);
        });

        // Handle view details button clicks
        $(document).on('click', '.view-details-btn', function(e) {
            e.stopPropagation();
            const id = $(this).data('id');

            // Show loading modal immediately
            $('#webhookDetailsModal').modal('show');

                $.ajax({
                        url: viewRoute.replace('::ID', id),
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        },
                        success: function(res) {
                            const data = res.data;
                            let contextData = data.data;
                            if (typeof contextData === 'string') {
                                try {
                                    contextData = JSON.parse(contextData);
                                } catch (e) {
                                    console.error('Invalid JSON string in context:', e);
                                    contextData = { error: 'Invalid JSON format' };
                                }
                            }

                            const contextHtml = renderJSON(contextData); 

                            const detailsHtml = `
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>IP Address: </strong>
                                    ${data.ip_address}
                                </div>
                                <div class="col-md-6">
                                    <strong>Order ID: </strong>
                                    <span class="badge badge-dark badge-type">${data.order_id}</span>
                                </div>
                            </div>
                            <hr/>
                            <h6>Content: </h6>
                            <div class="log-context">${contextHtml}</div>
                            `;
                            $('#webhook-details-content').html(detailsHtml);

                        }
                    });

            return false;

        });

        function renderJSON(obj, indent = 0) {
            let html = '';
            const padding = '&nbsp;'.repeat(indent * 4);

            for (const key in obj) {
                if (!obj.hasOwnProperty(key)) continue;

                const value = obj[key];

                if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                    html += `${padding}<strong>${key}</strong>: {<br>`;
                    html += renderJSON(value, indent + 1);
                    html += `${padding}}<br>`;
                } else if (Array.isArray(value)) {
                    html += `${padding}<strong>${key}</strong>: [${value.length > 0 ? "<br>" : ""}`;
                    value.forEach((item, index) => {
                        html += renderJSON(item, indent + 1);
                        if (index < value.length - 1) html += "<br>";
                    });
                    html += `${value.length > 0 ? padding : ""}]<br>`;
                } else {
                    html += `${padding}<strong>${key}</strong>: ${value}<br>`;
                }
            }

            return html;
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\PROJECTS\Max-life\rx-maxlife-panel\resources\views/webhook/index.blade.php ENDPATH**/ ?>