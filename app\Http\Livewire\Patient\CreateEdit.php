<?php

namespace App\Http\Livewire\Patient;

use App\Helpers\PatientHelper;
use App\Models\Patient;
use App\Models\State;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Livewire\Component;

class CreateEdit extends Component
{

    public $providers = [];
    public $states = [];
    public $patient;
    public $user;

    public function mount(Patient $patient)
    {
        $this->user = Auth::user();
        $this->patient = $patient;

        $this->loadProvider();
        $this->loadStates();
    }

    public function loadProvider()
    {
        $this->providers = User::where('role', User::ROLE_PROVIDER)->get();
    }

    public function rules()
    {
        $user = auth()->user();

        return [
            'patient.provider_id' => [
                Rule::when(
                    in_array($user->role, [User::ROLE_ADMIN, User::ROLE_OPERATOR]),
                    ['required', 'exists:users,id'],
                    ['nullable', 'exists:users,id']
                ),
            ],
            'patient.first_name' => 'required|regex:/^[A-Za-z\s]+$/',
            'patient.last_name' => 'required|regex:/^[A-Za-z\s]+$/',
            'patient.dob' => 'required|before:today',
            'patient.address' => 'required|regex:/^[A-Za-z0-9\s,\.\-]+$/',
            'patient.city' => 'required',
            'patient.state_id' => 'required|exists:states,id',
            'patient.zip_code' => 'required|regex:/^\d{5}(-\d{4})?$/',
            'patient.gender' => 'required',
            'patient.phone_number' => 'nullable|max:15',
        ];
    }

    public function messages()
    {
        return [
            'patient.first_name.required' => 'The first name field is required.',
            'patient.first_name.regex' => 'The first name must contain only alphabet and spaces.',
            'patient.last_name.required' => 'The last name field is required.',
            'patient.last_name.regex' => 'The last name must contain only alphabet and spaces.',
            'patient.dob.required' => 'The dob field is required.',
            'patient.dob.before' => 'The dob must be a date before today.',
            'patient.address.required' => 'The address field is required.',
            'patient.address.regex' => 'The address may only contain letters, numbers, spaces, commas, periods, and hyphens. no special characters.',
            'patient.city.required' => 'The city field is required.',
            'patient.state_id.required' => 'The state field is required.',
            'patient.state_id.exists' => 'The selected state is invalid.',
            'patient.zip_code.required' => 'The zip code field is required.',
            'patient.zip_code.regex' => 'The ZIP code must be in the format 12345 or 12345-6789. i.e Format of 5+4.',
            'patient.provider_id.required' => 'The provider field is required.',
            'patient.provider_id.exists' => 'The selected provider is invalid.',
            'patient.phone_number.max' => 'The phone number must be at most 15 digits.',
        ];
    }

    public function loadStates()
    {
        $this->states = State::orderBy('name')->get();
    }

    public function store()
    {
        $this->validate();

        $user = auth()->user();
        $this->patient->provider_id = $this->patient->provider_id ?? $user->id;
        $this->patient->dob = Carbon::parse($this->patient->dob)->format('Y-m-d');

        if (!$this->patient->id) {
            $result = PatientHelper::findOrCreatePatient(
                $this->patient->provider_id,
                $this->patient->first_name,
                $this->patient->last_name,
                $this->patient->dob,
            );

            if ($result['exists'] && $result['patient']->id !== $this->patient->id) {
                session()->flash('error-message', 'A patient with the same First Name, Last Name, DOB already exists.');
                return;
            }

            $existingPatient = $result['patient'];

            if (!$result['exists']) {
                $stateId = State::find($this->patient->state_id);

                $existingPatient->update([
                    'gender' => $this->patient->gender,
                    'address' => $this->patient->address,
                    'city' => $this->patient->city,
                    'state_id' => $stateId->id ?? null,
                    'zip_code' => $this->patient->zip_code,
                    'phone_number' => $this->patient->phone_number,
                ]);
                $this->patient = $existingPatient;
            }
        }

        $this->patient->save();

        session()->flash('success-message', __('messages.patient_created'));

        return redirect()->route('patients.index');
    }

    public function updated($propertyName)
    {
        // Clear validation errors as soon as user starts typing
        $this->validateOnly($propertyName);
    }

    public function render()
    {
        return view('livewire.patient.create-edit');
    }
}
