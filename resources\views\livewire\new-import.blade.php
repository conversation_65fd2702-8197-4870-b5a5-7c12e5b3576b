<div>
    @if (session()->has('message'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('message') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    <form wire:submit.prevent="savePrescription">
        <x-layout.row>
            <div class="col">
                {{-- <x-card> --}}
                <x-card.body>
                    {{-- @if (!$importFile->id)
                            <div class="mb-3 form-group" wire:ignore>
                                <label for="template" class="form-label">Template</label>
                                <select wire:model="selectedTemplate" id="template" class="form-control gender-select">
                                    <option value="">Select Template</option>
                                    @foreach ($templates as $template)
                                        <option value="{{ $template->id }}">{{ $template->title }}</option>
                                    @endforeach
                                </select>
                            </div>
                        @endif --}}
                    <div class="row">
                        <!-- Left Column -->
                        <div class="col-md-6 mt-4">
                            {{-- <h5 class="section-title">Patient Information</h5> --}}

                            <fieldset class="form-group-box">
                                <legend class="w-auto px-2 legend">Demographics</legend>
                                <!-- Script Date -->
                                <div class="mb-3">
                                    <x-form.input.text label="Script Date" labelRequired="1"
                                        model="script_date_display" id="script_date" readonly="readonly" />
                                    {{-- <small class="text-muted">Automatically filled with today's date but
                                            editable.</small> --}}
                                </div>
                                <!-- Last Name -->
                                <div class="mb-3">
                                    <div class="form-group">
                                        <label for="last_name" class="form-label">Last Name <span
                                                class="text-danger">*</span></label>
                                        <div class="input-group" id="last_name_group">
                                            <input type="text" id="last_name"
                                                class="form-control @error('importFile.last_name') is-invalid @enderror"
                                                wire:model.debounce.500ms="importFile.last_name"
                                                wire:input="checkAndResetPatientSelection($event.target.value, 'last_name')"
                                                wire:focus="$set('activeField', 'last_name')"
                                                wire:blur="$set('activeField', null)"
                                                @if (!$selectedPatientId) wire:keydown="searchPatients($event.target.value, 'last_name')"
                                                        wire:keydown.arrow-down="highlightNext"
                                                        wire:keydown.arrow-up="highlightPrevious"
                                                        wire:keydown.enter.prevent="selectHighlightedPatient" @endif
                                                placeholder="Enter last name">
                                            @if (count($matchingPatients) > 0 &&
                                                    $activeField === 'last_name' &&
                                                    !$selectedPatientId &&
                                                    !empty(trim($importFile->last_name)))
                                                <div class="dropdown-menu show w-100 mt-0" id="last_name_dropdown">
                                                    @foreach ($matchingPatients as $i => $patient)
                                                        <button type="button"
                                                            class="dropdown-item {{ $highlightedIndex === $i ? 'active' : '' }}"
                                                            wire:click="selectPatient({{ $patient->id }})">
                                                            <div>
                                                                <strong>
                                                                    {{ $patient->first_name }} {{ $patient->last_name }}
                                                                    <span
                                                                        class="text-muted">{{ $patient->dob ? date('m/d/Y', strtotime($patient->dob)) : '' }}
                                                                    </span>
                                                                </strong>
                                                            </div>
                                                        </button>
                                                    @endforeach
                                                </div>
                                            @endif
                                        </div>
                                        <x-form.error model="importFile.last_name" />
                                    </div>
                                </div>

                                <!-- First Name -->
                                <div class="mb-3">
                                    <div class="form-group">
                                        <label for="first_name" class="form-label">First Name <span
                                                class="text-danger">*</span></label>
                                        <div class="input-group" id="first_name_group">
                                            <input type="text" id="first_name"
                                                class="form-control @error('importFile.first_name') is-invalid @enderror"
                                                wire:model.debounce.500ms="importFile.first_name"
                                                wire:input="checkAndResetPatientSelection($event.target.value, 'first_name')"
                                                wire:focus="$set('activeField', 'first_name')"
                                                wire:blur="$set('activeField', null)"
                                                @if (!$selectedPatientId) wire:keydown="searchPatients($event.target.value, 'first_name')"
                                                        wire:keydown.arrow-down="highlightNext"
                                                        wire:keydown.arrow-up="highlightPrevious"
                                                        wire:keydown.enter.prevent="selectHighlightedPatient" @endif
                                                placeholder="Enter first name">
                                            @if (count($matchingPatients) > 0 &&
                                                    $activeField === 'first_name' &&
                                                    !$selectedPatientId &&
                                                    !empty(trim($importFile->first_name)))
                                                <div class="dropdown-menu show w-100 mt-0" id="first_name_dropdown">
                                                    @foreach ($matchingPatients as $i => $patient)
                                                        <button type="button"
                                                            class="dropdown-item {{ $highlightedIndex === $i ? 'active' : '' }}"
                                                            wire:click="selectPatient({{ $patient->id }})">
                                                            <div>
                                                                <strong>
                                                                    {{ $patient->first_name }} {{ $patient->last_name }}
                                                                    <span
                                                                        class="text-muted">{{ $patient->dob ? date('m/d/Y', strtotime($patient->dob)) : '' }}
                                                                    </span>
                                                                </strong>
                                                            </div>
                                                        </button>
                                                    @endforeach
                                                </div>
                                            @endif
                                        </div>
                                        <x-form.error model="importFile.first_name" />
                                    </div>
                                </div>

                                <!-- Date of Birth -->
                                <div class="mb-3">
                                    <x-form.input.text label="Date of Birth" labelRequired="1" model="dob_display"
                                        id="dob_individual_date_input" readonly="readonly"/>
                                </div>

                                <!-- Gender -->
                                <div class="mb-3">
                                    <div class="form-group">
                                        <div wire:ignore>
                                            <label for="gender" class="form-label">Gender</label><span
                                                class="text-danger">*</span>
                                            <select wire:model="importFile.gender" id="gender"
                                                class="form-control gender-select">
                                                <option value="">Select Gender</option>
                                                <option value="M">Male</option>
                                                <option value="F">Female</option>
                                            </select>
                                        </div>
                                        <x-form.error model="importFile.gender" />
                                    </div>
                                </div>
                            </fieldset>
                        </div>

                        <!-- Right Column -->
                        <div class="col-md-6 mt-4">
                            {{-- <h5 class="section-title">Contact Information</h5> --}}
                            <fieldset class="form-group-box">
                                <legend class="w-auto px-2 legend">Contact</legend>
                                <div class="mb-3">
                                    <x-form.input.text label="Address" labelRequired="1" model="importFile.address" />
                                </div>

                                <div class="mb-3">
                                    <x-form.input.text label="City" labelRequired="1" model="importFile.city" />
                                </div>

                                <div class="mb-3">
                                    <div class="form-group">
                                        <div wire:ignore>
                                            <label for="state" class="form-label">State</label><span
                                                class="text-danger">*</span>
                                            <select wire:model="importFile.state" id="state"
                                                class="form-control gender-select">
                                                <option value="">Select State</option>
                                                @foreach ($states as $state)
                                                    <option value="{{ $state->short_name }}"
                                                        data-id="{{ $state->id }}">
                                                        {{ $state->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <x-form.error model="importFile.state" />
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <x-form.input.text label="Zip Code" labelRequired="1" model="importFile.zip" />
                                </div>

                                <div class="mb-3">
                                    <x-form.input.text label="Phone" labelRequired="0" model="importFile.phone"
                                        type="number" />
                                </div>
                            </fieldset>
                        </div>
                    </div>

                    <!-- Medication Info -->
                    <fieldset class="form-group-box">
                        <legend class="w-auto px-2 legend">Medication</legend>
                        <div class="row mt-4">
                            <div class="col-12">
                                {{-- <h5 class="section-title">Medication Information</h5> --}}
                                <div class="mb-3">
                                    <div class="form-group" wire:ignore>
                                        <label labelRequired="1" for="medication" class="form-label">
                                            Medication
                                            <span class="text-danger">*</span>
                                        </label>
                                        <select label="medication" id="medication"
                                            class="form-control @error('importFile.medication') is-invalid @enderror gender-select"
                                            wire:model="importFile.medication" placeholder="Select medication">
                                            <option value="" selected>Select medication</option>
                                            @foreach ($medications as $medication)
                                                <option value="{{ $medication->name }}">{{ $medication->name }}
                                                </option>
                                            @endforeach
                                        </select>

                                        @error('importFile.medication')
                                            <span
                                                class="form-text text-danger"><strong>{{ $message }}</strong></span>
                                        @enderror
                                    </div>
                                </div>
                                {{-- <div class="mb-3">
                                        <x-form.input.text label="Strength" labelRequired="1"
                                            model="importFile.stregnth" />
                                    </div>

                                    <div class="mb-3">
                                        <x-form.input.text label="Dosing" labelRequired="1" model="importFile.dosing" />
                                    </div> --}}

                                <div class="mb-3">
                                    <x-form.input.text label="Refills" labelRequired="0" model="importFile.refills"
                                        type="number" />
                                </div>

                                <div class="mb-3">
                                    <x-form.input.text label="Vial Quantity" labelRequired="0"
                                        model="importFile.vial_quantity" type="number" />
                                </div>
                                <div class="mb-3">
                                    <x-form.input.text label="Days Supply" labelRequired="0"
                                        model="importFile.days_supply" type="number" min="0" />
                                </div>

                                <div class="mb-3" wire:ignore>
                                    <label for="ship_to" class="form-label">Ship To <span
                                            class="text-danger">*</span></label>
                                    <select wire:model="importFile.ship_to" id="ship_to"
                                        class="form-control @error('importFile.ship_to') is-invalid @enderror ship-select">
                                        <option value="patient" selected>Patient</option>
                                        <option value="practice">Practice</option>
                                    </select>
                                    @error('importFile.ship_to')
                                        <span class="form-text text-danger"><strong>{{ $message }}</strong></span>
                                    @enderror
                                </div>

                                <input type="hidden" id="comment" value="{{ $importFile->comment }}">
                                <input type="hidden" id="operator_name"
                                    value="{{ $importFile->returnedByUser ? $importFile->returnedByUser->first_name . ' ' . $importFile->returnedByUser->last_name : '' }}">
                                <input type="hidden" id="operator_email"
                                    value="{{ $importFile->returnedByUser ? $importFile->returnedByUser->email : '' }}">

                                <!-- Additional Information -->
                                <div class="row mt-4">
                                    <div class="col-md-6">
                                        {{-- <h5 class="section-title">Additional Information</h5> --}}
                                        <div class="mb-3">
                                            <x-form.input.textarea label="SIG" labelRequired="0"
                                                model="importFile.sig" />
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        {{-- <h5 class="section-title">Notes</h5> --}}
                                        <div class="mb-3">
                                            <x-form.input.textarea label="Note" labelRequired="0"
                                                model="importFile.notes" rows="3" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </fieldset>

                </x-card.body>

                <x-group.errors />

                <x-card.footer>
                    @if ($importFile->id)
                        <button type="submit" class="btn btn-primary px-5" wire:loading.attr="disabled"
                            wire:target="savePrescription">
                            <span wire:loading.remove wire:target="savePrescription">Save</span>
                            <span wire:loading wire:target="savePrescription" style="display: none;">
                                <i class="fas fa-spinner fa-spin mr-1"></i>
                                Saving...
                            </span>
                        </button>
                        @if (Auth::user()->role === \App\Models\User::ROLE_PROVIDER)
                            <button type="button" class="btn btn-primary px-5" wire:click="saveAndSignPrescription"
                                wire:loading.attr="disabled" wire:target="saveAndSignPrescription">
                                <span wire:loading.remove wire:target="saveAndSignPrescription">Save & Sign</span>
                                <span wire:loading wire:target="saveAndSignPrescription" style="display: none;">
                                    <i class="fas fa-spinner fa-spin mr-1"></i>
                                    Saving...
                                </span>
                            </button>
                        @endif
                    @else
                        <button type="submit" class="btn btn-primary px-5" wire:loading.attr="disabled"
                            wire:target="savePrescription">
                            <span wire:loading.remove wire:target="savePrescription">Save</span>
                            <span wire:loading wire:target="savePrescription" style="display: none;">
                                <i class="fas fa-spinner fa-spin mr-1"></i>
                                Saving...
                            </span>
                        </button>
                    @endif

                    <a href="{{ route('scripts.ready-to-sign') }}" class="btn btn-outline-secondary px-5">Cancel</a>

                </x-card.footer>
                {{-- </x-card> --}}
            </div>
        </x-layout.row>
    </form>

</div>
@section('styles')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        .select2-container--default .select2-selection--single {
            display: flex;
            align-items: center;
            height: 38px;
            /* match your input height */
        }

        .select2-selection__rendered {
            line-height: 1.6 !important;
        }

        .form-group-box {
            border: 3px solid #dff9ff;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1.5rem;
        }

        .legend {
            font-weight: bold;
            font-size: 1rem !important;
        }

        .dropdown-menu {
            position: absolute !important;
            top: 100% !important;
            left: 0 !important;
            z-index: 1000 !important;
            width: 100% !important;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 0.25rem 0.25rem;
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            cursor: pointer;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .dropdown-item small {
            display: block;
            margin-top: 0.25rem;
        }

        .dropdown-item.active,
        .dropdown-item:active {
            background-color: #dff9ff;
            color: #000;
        }
    </style>
@endsection
@push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        const comment = $('#comment').val();
        const operatorName = $('#operator_name').val();
        const operatorEmail = $('#operator_email').val();

        if (comment) {
            // Display comment in red alert container
            let commentHtml = `<div class="alert alert-danger mb-3 p-3">
                          <div class="w-100 text-break" style="word-break: break-word;">
                            <strong>Comment:</strong> ${comment}
                          </div>
                        </div>`;

            // Add operator information below without container if available
            if (operatorName && operatorEmail) {
                commentHtml += `<div class="text-right mb-3">
                          <span><strong>${operatorName}</strong> - ${operatorEmail}</span>
                        </div>`;
            }

            $('.card-header').html(commentHtml);
        }

        // this is to update the state selection when the state is changed from the select dropdown
        // Function to show dropdown for a specific field
        function showDropdown(field) {
            // Hide all dropdowns first
            document.querySelectorAll('.dropdown-menu').forEach(dropdown => {
                dropdown.style.display = 'none';
            });

            // Show the selected dropdown
            const dropdown = document.getElementById(field + '_dropdown');
            if (dropdown) {
                dropdown.style.display = 'block';
            }
        }

        function initializeFlatpickrModals() {
            // Initialize Flatpickr for Script Date input with future date restriction
            if (document.getElementById('script_date')) {
                flatpickr('#script_date', {
                    dateFormat: "m-d-Y",
                    allowInput: true,
                    clickOpens: true,
                    maxDate: "today",
                    defaultDate: "today",
                    onReady: function(selectedDates, dateStr, instance) {
                        // Set default to today if no value
                        if (!instance.input.value) {
                            instance.setDate(new Date(), false);
                        }
                    }
                });
            }

            // Initialize Flatpickr for DOB modal input with future date restriction
            if (document.getElementById('dob_individual_date_input')) {
                const dobInput = document.getElementById('dob_individual_date_input');
                const existingValue = dobInput.value;

                flatpickr('#dob_individual_date_input', {
                    dateFormat: "m-d-Y",
                    allowInput: true,
                    clickOpens: true,
                    maxDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
                    onReady: function(selectedDates, dateStr, instance) {
                        // Only set default year/month if there's no existing value
                        if (!existingValue || existingValue.trim() === '') {
                            instance.currentYear = 1990;
                            instance.currentMonth = 0; // January
                            instance.redraw();
                        }
                    }
                });
            }
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.input-group')) {
                document.querySelectorAll('.dropdown-menu').forEach(dropdown => {
                    dropdown.style.display = 'none';
                });
            }
        });

        // Close dropdown on escape key
        // document.addEventListener('keydown', function(event) {
        //     if (event.key === 'Escape') {
        //         document.querySelectorAll('.dropdown-menu').forEach(dropdown => {
        //             dropdown.style.display = 'none';
        //         });
        //     }
        // });

        document.addEventListener('DOMContentLoaded', function() {
            window.addEventListener('dropdown-changed', event => {
                $('#state').val(event.detail.state).trigger('change');
                $('#gender').val(event.detail.gender).trigger('change');
            });

            window.addEventListener('template-dropdown-changed', event => {
                $('#medication').val(event.detail.medication).trigger('change');
            });
            initializeFlatpickrModals();
        });

        $('#state').on('change', function(e) {
            let selected = $(this).find('option:selected');
            let state = {
                id: selected.data('id'),
                name: selected.val(),
            };

            // Send the whole object to Livewire
            @this.getStateId(state);
        });

        $(document).ready(function() {
            $('#template').select2({
                placeholder: "Select Template",
            }).on('change', function(e) {
                @this.set('selectedTemplate', $(e.target).val());
            });

            $('#state').select2({
                placeholder: "Select State",
            }).on('change', function(e) {
                @this.set('importFile.state', $(e.target).val());
            });

            $('#gender').select2({
                placeholder: "Select Gender",
            }).on('change', function(e) {
                @this.set('importFile.gender', $(e.target).val());
            });

            $('#medication').select2({
                placeholder: "Select Medication",
            }).on('change', function(e) {
                @this.set('importFile.medication', $(e.target).val());
            });

            $('#ship_to').select2({
                placeholder: "Select Ship To",
            }).on('change', function(e) {
                @this.set('importFile.ship_to', $(e.target).val());
            });

            // Set initial value for ship_to if not already set
            if (!@this.get('importFile.ship_to')) {
                @this.set('importFile.ship_to', 'patient');
                $('#ship_to').val('patient').trigger('change');
            }
        });
    </script>
@endpush
