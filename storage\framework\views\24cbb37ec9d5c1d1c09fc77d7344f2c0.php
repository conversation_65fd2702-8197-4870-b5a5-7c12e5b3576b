

<?php
    use App\Models\User;
    $user = Auth::user();
?>

<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    

                    <div class="card-body">
                        <?php if(session('status')): ?>
                            <div class="alert alert-success" role="alert">
                                <?php echo e(session('status')); ?>

                            </div>
                        <?php endif; ?>

                        <?php if(session('error')): ?>
                            <p class="text-danger" role="alert" style="font-size: 1.5rem;">
                                <?php echo e(session('error')); ?>

                            </p>
                        <?php endif; ?>

                        <!-- Display no data error message -->
                        <?php if(session('no_data_error')): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert" style="margin-bottom: 20px;">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                <?php echo e(session('no_data_error')); ?>

                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        <?php endif; ?>

                        <!-- Progress Steps -->
                        <div class="progress-steps-container mb-5">
                            <div class="progress-line">
                                <div class="progress-line-inner"></div>
                            </div>
                            <div class="progress-steps">
                                <div class="progress-step active">
                                    <div class="step-circle">1</div>
                                    <div class="step-label">Upload</div>
                                </div>
                                <div class="progress-step">
                                    <div class="step-circle">2</div>
                                    <div class="step-label">Create</div>
                                </div>
                                <div class="progress-step">
                                    <div class="step-circle">3</div>
                                    <div class="step-label">Sign</div>
                                </div>
                                <div class="progress-step">
                                    <div class="step-circle">4</div>
                                    <div class="step-label">Done</div>
                                </div>
                            </div>
                        </div>

                        <div class="row justify-content-center">
                            <div class="col-md-8">
                                <h6 class="mb-3">Select Excel File</h6>
                                <form action="<?php echo e(route('center-fill.store')); ?>" method="POST" enctype="multipart/form-data">
                                    <?php echo csrf_field(); ?>

                                    <?php if($user->role !== User::ROLE_PROVIDER): ?>
                                        <!-- Provider Selection -->
                                    <div class="form-group mb-4">
                                        <label for="provider_id" class="form-label font-weight-bold">Provider </label>
                                        <select name="provider_id" id="provider_id"
                                            class="form-control <?php $__errorArgs = ['provider_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required
                                            data-width="100%">
                                            <option value="">Select a Provider</option>
                                            <?php $__currentLoopData = $providers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $provider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($provider->id); ?>"
                                                    <?php echo e(old('provider_id') == $provider->id ? 'selected' : ''); ?>>
                                                    <?php echo e($provider->first_name); ?> <?php echo e($provider->last_name); ?>

                                                    <?php if($provider->clinic_name): ?>
                                                        - <?php echo e($provider->clinic_name); ?>

                                                    <?php endif; ?>
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                        <?php $__errorArgs = ['provider_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback" role="alert">
                                                <strong><?php echo e($message); ?></strong>
                                            </span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <small class="form-text text-muted">
                                            Select the provider who will own these imported scripts. The scripts will
                                            appear
                                            in their "Ready to Sign" section.
                                        </small>
                                    </div>
                                        
                                    <?php endif; ?>
                                    <div class="form-group">
                                        <div class="custom-file">
                                            <input type="file" name="excel_file"
                                                class="custom-file-input <?php $__errorArgs = ['excel_file'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                id="excel_file" accept=".xlsx,.xls" style="opacity: 0; position: absolute;">
                                            <div class="d-flex">
                                                <input type="text" class="form-control" id="file_name_display"
                                                    placeholder="No file selected" readonly>
                                                <button type="button" class="btn btn-secondary"
                                                    onclick="document.getElementById('excel_file').click()">Browse</button>
                                            </div>
                                            <?php $__errorArgs = ['excel_file'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <span class="invalid-feedback" role="alert">
                                                    <strong><?php echo e($message); ?></strong>
                                                </span>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                        <small class="form-text text-muted">Upload an Excel file (.xlsx or
                                            .xls)</small>
                                    </div>
                                    <div class="text-center mt-4">
                                        <button type="submit" class="btn btn-danger px-4" id="import-btn" style="display: none;">Import</button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <div class="text-end me-3">
                            <a href="/sample_files/Template.xlsx" download="Template.xlsx"
                                class="download-template">Download Excel template</a>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <style>
        .download-template {
            position: absolute;
            top: 20px;
            right: 30px;
            color: #6b21a8;
            font-weight: 500;
            text-decoration: underline;
            font-size: 16px;
            /* Increase this if you want it even bigger */
        }



        .card {
            border: none;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            border-radius: 0;
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid #f1f1f1;
            padding: 15px 20px;
        }

        /* Progress Steps Styling */
        .progress-steps-container {
            position: relative;
            padding: 20px 0;
            margin: 0 auto;
            max-width: 700px;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            position: relative;
            z-index: 1;
        }

        .progress-step {
            text-align: center;
            width: 20%;
            position: relative;
        }

        .step-circle {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-weight: 500;
            font-size: 14px;
        }

        .progress-step.active .step-circle {
            background-color: #000000;
            color: white;
        }

        .step-label {
            font-size: 13px;
            color: #6c757d;
            font-weight: 400;
        }

        .progress-step.active .step-label {
            color: #000000;
            font-weight: 500;
        }

        .progress-line {
            position: absolute;
            top: 38px;
            left: 10%;
            right: 10%;
            height: 2px;
            background-color: #e9ecef;
            z-index: 0;
        }

        .progress-line-inner {
            height: 100%;
            width: 20%;
            background-color: #000000;
        }

        .btn-danger {
            background-color: #000000;
            border-color: #000000;
        }

        .custom-file-label {
            border-radius: 0;
        }

        .custom-file-label::after {
            background-color: #f8f9fa;
            color: #212529;
            border-radius: 0;
        }

        /* Custom file input styling */
        .form-control {
            border-radius: 0;
            border-right: none;
            a
        }

        .btn-secondary {
            background-color: #f8f9fa;
            color: #212529;
            border-color: #ced4da;
            border-radius: 0;
            border-left: none;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        // Store the Excel import form route for targeting
        var excelImportFormRoute = "<?php echo e(route('center-fill.store')); ?>";

        // Display file name when selected
        document.getElementById('excel_file').addEventListener('change', function(e) {
            var importBtn = document.getElementById('import-btn');
            if (e.target.files.length > 0) {
                // Get just the filename without any path
                var fileName = e.target.files[0].name;

                // Update the text input with just the filename
                document.getElementById('file_name_display').value = fileName;

                // Show the import button
                if (importBtn) {
                    importBtn.style.display = 'inline-block';
                    importBtn.disabled = false;
                }
            } else {
                // Hide the import button if no file is selected
                if (importBtn) {
                    importBtn.style.display = 'none';
                }
                document.getElementById('file_name_display').value = '';
            }
        });

        // Form validation - specifically target the Excel import form
        document.addEventListener('DOMContentLoaded', function() {
            // Find the Excel import form
            var excelForm = document.querySelector('form[action="' + excelImportFormRoute + '"]');

            if (excelForm) {
                // Add submit event listener only to the Excel import form
                excelForm.addEventListener('submit', function(e) {
                    var fileInput = document.getElementById('excel_file');
                    if (fileInput.files.length === 0) {
                        e.preventDefault();
                        alert('Please select an Excel file to import');
                        return false;
                    }

                    // Show loading state - specifically target the import button by ID
                    var submitButton = this.querySelector('#import-btn');
                    if (submitButton) {
                        submitButton.innerHTML =
                            '<span class="spinner-border spinner-border-sm mr-2" role="status" aria-hidden="true"></span> Importing...';
                        submitButton.disabled = true;
                    }
                });
                
            }
        });
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Select2 for provider dropdown
            $('#provider_id').select2({
                placeholder: 'Select a Provider',
                // allowClear: true,
                // width: '100%'
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\PROJECTS\Max-life\rx-maxlife-panel\resources\views/centerfill/excel-import/form.blade.php ENDPATH**/ ?>